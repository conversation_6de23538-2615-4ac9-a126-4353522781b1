<?php
require_once 'includes/config.php';
require_once 'includes/languages.php';
include 'includes/header.php';
?>

<!-- Hero Section -->
<div class="bg-white min-h-screen flex items-center relative overflow-hidden">
    <main class="w-full">
        <div class="max-w-6xl mx-auto px-6 py-20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <!-- Left Content -->
                <div class="space-y-8">
                    <div class="hero-title-container">
                        <h1 class="text-5xl lg:text-6xl font-light leading-tight text-gray-900">
                            <span class="text-primary"><?php echo __('hero_title_rhapsody'); ?> <span id="typewriter-text" class="font-display font-semibold bg-gradient-to-r from-red-600 via-red-500 to-orange-500 bg-clip-text text-transparent drop-shadow-sm uppercase"><?php echo __('typewriter_crusades'); ?><span id="cursor" class="animate-pulse text-red-500 font-bold">|</span></span></span>
                        </h1>
                    </div>

                    <p class="text-xl text-gray-600 leading-relaxed font-light">
                        <?php echo __('hero_description'); ?>
                    </p>

                    <div class="pt-4">
                        <a href="#about" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                            <?php echo __('learn_more'); ?>
                            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Right Image -->
                <div class="flex justify-center lg:justify-end">
                    <div class="w-full max-w-md">
                        <img
                            class="w-full h-auto"
                            src="assets/images/hero.png"
                            alt="<?php echo __('hero_alt_text'); ?>"
                            loading="lazy"
                        >
                    </div>
                </div>

            </div>
        </div>
    </main>
</div>

<!-- Scroll Indicator -->
<div class="hidden md:block absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
    <div class="flex flex-col items-center space-y-3 group cursor-pointer" onclick="document.getElementById('about').scrollIntoView({behavior: 'smooth'})">
        <span class="text-xs font-medium text-gray-400 uppercase tracking-wider group-hover:text-primary transition-colors duration-300"><?php echo __('scroll_text'); ?></span>
        <div class="w-px h-8 bg-gray-300 group-hover:bg-primary transition-colors duration-300"></div>
        <div class="animate-bounce">
            <svg class="w-4 h-4 text-gray-400 group-hover:text-primary transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </div>
</div>

<!-- Events Section -->
<section id="about" class="py-24 bg-gray-50">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-20">
            <h2 class="text-4xl font-light text-gray-900 mb-4">Upcoming Crusades</h2>
            <div class="w-16 h-px bg-primary mx-auto"></div>
        </div>

        <div class="relative">
            <?php
            // Load crusades from JSON file
            $crusadesJson = file_get_contents(__DIR__ . '/data/crusades.json');
            $crusadesData = json_decode($crusadesJson, true);
            $currentDate = new DateTime();
            ?>
            <!-- Carousel container -->
            <div id="crusadesCarousel" class="overflow-hidden">
                <div id="crusadesSlider" class="flex transition-transform duration-500 ease-in-out">
                    <?php foreach ($crusadesData['crusades'] as $crusade): 
                        $eventDate = new DateTime($crusade['date'] . ' ' . $crusade['time']);
                        $isPastEvent = $eventDate < $currentDate;
                        $eventClass = $isPastEvent ? 'opacity-60' : '';
                    ?>
                    <div class="w-full flex-shrink-0 px-4 sm:px-6">
                        <div class="bg-white border border-gray-200 overflow-hidden <?php echo $eventClass; ?>">
                            <div class="relative flex flex-col lg:flex-row">
                                <!-- Past Event Badge -->
                                <?php if ($isPastEvent): ?>
                                <div class="absolute top-4 right-4 bg-red-600 text-white text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md">
                                    Past Event
                                </div>
                                <?php endif; ?>
                                <!-- Image Section -->
                                <div class="w-full lg:w-1/2 relative">
                                    <div class="aspect-[16/9] sm:aspect-[4/3] lg:aspect-auto lg:h-full overflow-hidden">
                                        <img class="w-full h-full object-cover" src="<?php echo htmlspecialchars($crusade['image']); ?>" alt="<?php echo htmlspecialchars($crusade['title']); ?>">
                                    </div>
                                </div>

                                <!-- Content Section -->
                                <div class="w-full lg:w-1/2 p-6 sm:p-8 lg:p-12 flex flex-col justify-center">
                                    <div class="space-y-6 lg:space-y-8">
                                        <h3 class="text-2xl sm:text-3xl lg:text-4xl font-light text-gray-900 leading-tight"><?php echo htmlspecialchars($crusade['title']); ?></h3>
                                        <p class="text-base sm:text-lg text-gray-600 leading-relaxed"><?php echo htmlspecialchars($crusade['description']); ?></p>

                                        <!-- Event Details -->
                                        <div class="space-y-3 sm:space-y-4">
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                <div class="flex flex-col sm:flex-row sm:items-center">
                                                    <span class="text-sm font-medium text-gray-500 sm:w-20 sm:mr-2"><?php echo __('event_date'); ?>:</span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo $eventDate->format('F j, Y'); ?> at <?php echo $eventDate->format('g:i A'); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <div class="flex flex-col">
                                                    <span class="text-sm font-medium text-gray-500"><?php echo __('venue'); ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo htmlspecialchars($crusade['venue']); ?><br>
                                                        <?php echo htmlspecialchars($crusade['address']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <?php if (!$isPastEvent): ?>
                                        <div class="pt-2">
                                            <a href="<?php echo htmlspecialchars($crusade['register_link']); ?>" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                                                <?php echo __('register_now'); ?>
                                                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <button id="prevButton" class="absolute top-1/2 -left-2 sm:-left-8 lg:-left-16 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button id="nextButton" class="absolute top-1/2 -right-2 sm:-right-8 lg:-right-16 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Carousel Indicators -->
            <div id="dotsContainer" class="flex justify-center mt-16 space-x-4">
                <?php foreach ($crusadesData['crusades'] as $index => $crusade): ?>
                <button
                    onclick="goToSlide(<?php echo $index; ?>)"
                    aria-label="Go to slide <?php echo $index + 1; ?>"
                    data-index="<?php echo $index; ?>"
                    class="w-3 h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 <?php echo $index === 0 ? 'bg-primary' : ''; ?>"
                ></button>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Give Section -->
<section id="give" class="py-24 bg-white">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-20">
            <h2 class="text-4xl font-light text-gray-900 mb-4"><?php echo __('give_title'); ?></h2>
            <p class="text-xl text-gray-600 mb-6"><?php echo __('give_subtitle'); ?></p>
            <div class="w-16 h-px bg-primary mx-auto"></div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <!-- Left Content -->
            <div class="space-y-8">
                <h3 class="text-3xl font-light text-gray-900"><?php echo __('give_join_us'); ?></h3>
                <p class="text-gray-600 leading-relaxed">
                    <?php echo __('give_description'); ?>
                </p>

                <div class="space-y-4">
                    <div class="flex items-center text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <?php echo __('give_sponsor_crusades'); ?>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <?php echo __('give_fund_outreach'); ?>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <?php echo __('give_enable_distribution'); ?>
                    </div>
                </div>
            </div>

            <!-- Right Payment Form -->
            <div class="bg-gray-50 p-8 border border-gray-200">
                <h4 class="text-2xl font-light text-gray-900 mb-6"><?php echo __('give_select_amount'); ?></h4>

                <!-- Preset Amounts -->
                <div class="grid grid-cols-3 gap-3 mb-6">
                    <button onclick="selectAmount(25)" class="amount-btn p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200">
                        <div class="text-lg font-medium">$25</div>
                    </button>
                    <button onclick="selectAmount(50)" class="amount-btn p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200">
                        <div class="text-lg font-medium">$50</div>
                    </button>
                    <button onclick="selectAmount(100)" class="amount-btn p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200">
                        <div class="text-lg font-medium">$100</div>
                    </button>
                </div>

                <!-- Custom Amount -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('give_custom_amount'); ?></label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input type="number" id="customAmount" placeholder="<?php echo __('give_enter_amount'); ?>"
                               class="w-full pl-8 pr-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                               min="1" step="0.01">
                    </div>
                </div>

                <!-- Donor Information -->
                <div class="space-y-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('give_full_name'); ?></label>
                        <input type="text" id="donorName"
                               class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                               placeholder="<?php echo __('give_enter_full_name'); ?>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('give_email_address'); ?></label>
                        <input type="email" id="donorEmail"
                               class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                               placeholder="<?php echo __('give_enter_email'); ?>">
                    </div>
                </div>

                <!-- Payment Button -->
                <button id="donate-button" onclick="processDonation()"
                        class="w-full py-4 bg-primary text-white font-medium hover:bg-red-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                    <?php echo __('give_donate_now'); ?>
                </button>

                <!-- Security Notice -->
                <div class="mt-4 text-center">
                    <p class="text-xs text-gray-500">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        <?php echo __('give_secure_payment'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Products Section -->
<section id="products" class="py-24 bg-white">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-20">
            <h2 class="text-4xl font-light text-gray-900 mb-4"><?php echo __('our_products'); ?></h2>
            <div class="w-16 h-px bg-primary mx-auto"></div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Rhapsody Daily Card -->
            <div class="group">
                <div class="aspect-[4/3] overflow-hidden mb-6">
                    <img class="w-full h-full object-cover" src="assets/images/rhapsody.png" alt="<?php echo __('rhapsody_daily'); ?>">
                </div>
                <div class="space-y-4">
                    <h3 class="text-xl font-light text-gray-900"><?php echo __('rhapsody_daily'); ?></h3>
                    <p class="text-gray-600 text-sm leading-relaxed"><?php echo __('rhapsody_desc'); ?></p>
                    <a href="https://rhapsodyofrealities.org/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                        <?php echo __('learn_more_btn'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Rhapsody TV Card -->
            <div class="group">
                <div class="aspect-[4/3] overflow-hidden mb-6">
                    <img class="w-full h-full object-cover" src="assets/images/rtv.jpg" alt="<?php echo __('rhapsody_tv_title'); ?>">
                </div>
                <div class="space-y-4">
                    <h3 class="text-xl font-light text-gray-900"><?php echo __('rhapsody_tv_title'); ?></h3>
                    <p class="text-gray-600 text-sm leading-relaxed"><?php echo __('rhapsody_tv_desc'); ?></p>
                    <a href="https://rhapsodytv.live/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                        <?php echo __('learn_more_btn'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Reach Out World Card -->
            <div class="group">
                <div class="aspect-[4/3] overflow-hidden mb-6">
                    <img class="w-full h-full object-cover" src="assets/images/reachout.jpg" alt="<?php echo __('reachout_title'); ?>">
                </div>
                <div class="space-y-4">
                    <h3 class="text-xl font-light text-gray-900"><?php echo __('reachout_title'); ?></h3>
                    <p class="text-gray-600 text-sm leading-relaxed"><?php echo __('reachout_desc'); ?></p>
                    <a href="https://reachoutworld.org/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                        <?php echo __('learn_more_btn'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Tap2Read Card -->
            <div class="group">
                <div class="aspect-[4/3] overflow-hidden mb-6">
                    <img class="w-full h-full object-cover" src="https://rhapsodyofrealities.b-cdn.net/rhapsodyofrealities.org/card_images/2024/TAP2READAPP%20(1).jpg" alt="<?php echo __('tap2read_title'); ?>">
                </div>
                <div class="space-y-4">
                    <h3 class="text-xl font-light text-gray-900"><?php echo __('tap2read_title'); ?></h3>
                    <p class="text-gray-600 text-sm leading-relaxed"><?php echo __('tap2read_desc'); ?></p>
                    <a href="https://rhapsodytaptoread.org" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                        <?php echo __('order_now'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- App Download Section -->
<section class="py-24 bg-gray-50">
    <div class="max-w-6xl mx-auto px-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div class="order-2 lg:order-1 space-y-8">
                <h2 class="text-4xl font-light text-gray-900"><?php echo __('download_app_title'); ?></h2>
                <p class="text-gray-600 leading-relaxed"><?php echo __('download_app_desc'); ?></p>
                <div>
                    <button
                        onclick="openDownloadModal()"
                        class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200"
                    >
                        <?php echo __('download_app_btn'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="order-1 lg:order-2">
                <div class="aspect-[4/3] overflow-hidden">
                    <img class="w-full h-full object-cover" src="https://rhapsodyofrealities.b-cdn.net/rhapsodyofrealities.org/card_images/2025/app-download_jan_2025.jpg" alt="<?php echo __('download_app'); ?>">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Download Modal -->
<div id="downloadModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-30">
    <div class="bg-white max-w-lg w-full mx-6 border border-gray-200">
        <div class="p-12">
            <!-- Header -->
            <div class="flex justify-between items-start mb-8">
                <h3 class="text-2xl font-light text-gray-900"><?php echo __('download_app_modal_title'); ?></h3>
                <button onclick="closeDownloadModal()" class="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Download Options -->
            <div class="space-y-6">
                <!-- LoveWorld App Store -->
                <div class="group">
                    <a href="https://web.lwappstore.com/share/appId-32181354074e5cf63319371178894acd" target="_blank"
                       class="block p-6 border border-gray-200 hover:border-primary transition-colors duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2"><?php echo __('download_loveworld'); ?></h4>
                                <p class="text-sm text-gray-600">Official LoveWorld App Store</p>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </a>
                </div>

                <!-- Google Play Store -->
                <div class="group">
                    <a href="https://play.google.com/store/apps/details?id=com.rhapsodyreader&pli=1" target="_blank"
                       class="block p-6 border border-gray-200 hover:border-primary transition-colors duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2"><?php echo __('download_playstore'); ?></h4>
                                <p class="text-sm text-gray-600">Available on Google Play</p>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </a>
                </div>

                <!-- App Store (if available) -->
                <div class="group">
                    <a href="https://apps.apple.com/app/rhapsody-of-realities/id1114966583" target="_blank"
                       class="block p-6 border border-gray-200 hover:border-primary transition-colors duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2">Download from App Store</h4>
                                <p class="text-sm text-gray-600">Available on iOS App Store</p>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Footer Note -->
            <div class="mt-8 pt-6 border-t border-gray-100">
                <p class="text-sm text-gray-500 text-center">Choose your preferred platform to download the app</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom Animations for Tailwind */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Default state for scroll animations */
.scroll-element, .scroll-element-left, .scroll-element-right, .scroll-card {
    opacity: 0;
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-element.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-element-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-element-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-card.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Initial positions */
.scroll-element {
    transform: translateY(30px);
}

.scroll-element-left {
    transform: translateX(-30px);
}

.scroll-element-right {
    transform: translateX(30px);
}

.scroll-card {
    transform: translateY(30px);
}
</style>

<script>
function openDownloadModal() {
    document.getElementById('downloadModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeDownloadModal() {
    document.getElementById('downloadModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('downloadModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDownloadModal();
    }
});

// Scroll animation function
function initScrollAnimations() {
    const scrollElements = document.querySelectorAll('.scroll-element, .scroll-element-left, .scroll-element-right');
    const scrollCards = document.querySelectorAll('.scroll-card');

    // Check if element is in viewport
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8
        );
    }

    // Handle scroll animation
    function handleScrollAnimation() {
        // Animate sections
        scrollElements.forEach(element => {
            if (isElementInViewport(element) && !element.classList.contains('visible')) {
                element.classList.add('visible');
            }
        });

        // Animate cards with delay
        scrollCards.forEach(card => {
            if (isElementInViewport(card) && !card.classList.contains('visible')) {
                const delay = card.getAttribute('data-delay') || 0;
                setTimeout(() => {
                    card.classList.add('visible');
                }, delay);
            }
        });
    }

    // Run on scroll
    window.addEventListener('scroll', handleScrollAnimation);

    // Run on load
    handleScrollAnimation();
}

// Smooth scroll for anchors
document.addEventListener('DOMContentLoaded', function() {
    const learnMoreBtn = document.querySelector('a[href="#about"]');

    if (learnMoreBtn) {
        learnMoreBtn.addEventListener('click', function(e) {
            e.preventDefault();

            const targetSection = document.getElementById('about');
            if (targetSection) {
                // Get the header height to adjust scroll position
                const headerHeight = document.querySelector('header').offsetHeight;

                // Calculate the top position of the target section
                const targetPosition = targetSection.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                // Smooth scroll to the target
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    }
});

// Enhanced carousel functionality
function goToSlide(index) {
    const slider = document.getElementById('crusadesSlider');
    const dots = document.querySelectorAll('#dotsContainer button');

    if (slider) {
        slider.style.transform = `translateX(-${index * 100}%)`;

        // Update active dot
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.classList.add('bg-primary');
                dot.classList.remove('bg-gray-300');
            } else {
                dot.classList.remove('bg-primary');
                dot.classList.add('bg-gray-300');
            }
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const prevButton = document.getElementById('prevButton');
    const nextButton = document.getElementById('nextButton');
    const slider = document.getElementById('crusadesSlider');
    const dots = document.querySelectorAll('#dotsContainer button');

    if (prevButton && nextButton && slider) {
        let currentIndex = 0;
        const slides = slider.children.length;

        // Initialize first dot as active
        if (dots.length > 0) {
            dots[0].classList.add('bg-primary');
            dots[0].classList.remove('bg-gray-300');
        }

        prevButton.addEventListener('click', function() {
            currentIndex = (currentIndex - 1 + slides) % slides;
            goToSlide(currentIndex);
        });

        nextButton.addEventListener('click', function() {
            currentIndex = (currentIndex + 1) % slides;
            goToSlide(currentIndex);
        });

        // Auto-advance carousel every 5 seconds
        setInterval(function() {
            currentIndex = (currentIndex + 1) % slides;
            goToSlide(currentIndex);
        }, 5000);
    }

    // Initialize scroll animations
    initScrollAnimations();
});

// Typewriter Animation
const words = [
    '<?php echo __('typewriter_crusades'); ?>',
    '<?php echo __('typewriter_conferences'); ?>',
    '<?php echo __('typewriter_outreaches'); ?>',
    '<?php echo __('typewriter_rallies'); ?>',
    '<?php echo __('typewriter_distributions'); ?>'
];
let currentWordIndex = 0;
let currentCharIndex = 0;
let isDeleting = false;
let typeSpeed = 150;
let deleteSpeed = 100;
let pauseTime = 2000;

function typeWriter() {
    const typewriterElement = document.getElementById('typewriter-text');
    const cursorElement = document.getElementById('cursor');
    const currentWord = words[currentWordIndex];

    if (isDeleting) {
        // Deleting characters
        const displayText = currentWord.substring(0, currentCharIndex - 1);
        typewriterElement.innerHTML = displayText + '<span id="cursor" class="animate-pulse text-red-500 font-bold">|</span>';
        currentCharIndex--;

        if (currentCharIndex === 0) {
            isDeleting = false;
            currentWordIndex = (currentWordIndex + 1) % words.length;
            // Add scaling animation when starting new word
            typewriterElement.classList.add('text-changing');
            setTimeout(() => {
                typewriterElement.classList.remove('text-changing');
            }, 300);
            setTimeout(typeWriter, 500); // Pause before typing next word
            return;
        }
        setTimeout(typeWriter, deleteSpeed);
    } else {
        // Typing characters
        const displayText = currentWord.substring(0, currentCharIndex + 1);
        typewriterElement.innerHTML = displayText + '<span id="cursor" class="animate-pulse text-red-500 font-bold">|</span>';
        currentCharIndex++;

        if (currentCharIndex === currentWord.length) {
            isDeleting = true;
            // Add scaling animation when word is complete
            typewriterElement.classList.add('text-changing');
            setTimeout(() => {
                typewriterElement.classList.remove('text-changing');
            }, 300);
            setTimeout(typeWriter, pauseTime); // Pause when word is complete
            return;
        }
        setTimeout(typeWriter, typeSpeed);
    }
}

// Start typewriter animation when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(typeWriter, 1000); // Start after 1 second
});
</script>

<style>
/* Enhanced typewriter animation styles */
#typewriter-text {
    position: relative;
    display: inline-block;
    animation: textGlow 3s ease-in-out infinite alternate;
    white-space: nowrap;
    min-width: 250px;
}

#cursor {
    animation: cursorBlink 1s infinite;
}

@keyframes textGlow {
    0% {
        text-shadow: 0 0 5px rgba(220, 38, 38, 0.3);
    }
    100% {
        text-shadow: 0 0 20px rgba(220, 38, 38, 0.6), 0 0 30px rgba(220, 38, 38, 0.4);
    }
}

@keyframes cursorBlink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* Add a subtle scale animation when text changes */
.text-changing {
    animation: textScale 0.3s ease-in-out;
}

@keyframes textScale {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Mobile-specific fixes for typewriter animation */
@media (max-width: 768px) {
    #typewriter-text {
        min-width: 200px;
    }
    
    .hero-title-container {
        position: relative;
        min-height: 3.5rem;
        overflow: visible;
    }
}
</style>

<!-- SweetAlert2 and Animate.css -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Make translations available to JavaScript -->
<script>
window.translations = {
    payment_complete_giving: '<?php echo addslashes(__('payment_complete_giving')); ?>',
    payment_giving_amount: '<?php echo addslashes(__('payment_giving_amount')); ?>',
    payment_donor: '<?php echo addslashes(__('payment_donor')); ?>',
    payment_complete_giving_btn: '<?php echo addslashes(__('payment_complete_giving_btn')); ?>',
    payment_processing: '<?php echo addslashes(__('payment_processing')); ?>',
    payment_secure_stripe: '<?php echo addslashes(__('payment_secure_stripe')); ?>',
    success_thank_you: '<?php echo addslashes(__('success_thank_you')); ?>',
    success_message: '<?php echo addslashes(__('success_message')); ?>',
    success_continue: '<?php echo addslashes(__('success_continue')); ?>'
};
</script>

<!-- Stripe and Donation Scripts -->
<script src="https://js.stripe.com/v3/"></script>
<script src="assets/js/donation.js"></script>

<?php include 'includes/footer.php'; ?>