<?php
require_once '../includes/config.php';
require_once '../includes/languages.php';
require_once '../includes/TranslationService.php';

// Load and process crusades data
$crusadesData = json_decode(file_get_contents('../data/crusades.json'), true);
$crusades = $crusadesData['crusades'];

// Function to get approximate coordinates for cities/countries
function getCoordinates($address) {
    $address = strtolower($address);
    
    // Major cities and countries coordinates
    $coordinates = [
        'lagos' => ['lat' => 6.5244, 'lng' => 3.3792],
        'nigeria' => ['lat' => 9.0820, 'lng' => 8.6753],
        'caracas' => ['lat' => 10.4806, 'lng' => -66.9036],
        'venezuela' => ['lat' => 6.4238, 'lng' => -66.5897],
        'sta teresa del tuy' => ['lat' => 10.2297, 'lng' => -66.6502],
        'chennai' => ['lat' => 13.0827, 'lng' => 80.2707],
        'india' => ['lat' => 20.5937, 'lng' => 78.9629],
        // Add more locations as needed
    ];
    
    foreach ($coordinates as $location => $coords) {
        if (strpos($address, $location) !== false) {
            return $coords;
        }
    }
    
    // Default fallback coordinates
    return ['lat' => 0, 'lng' => 0];
}

// Function to determine continent based on address
function getContinent($address) {
    $address = strtolower($address);
    
    if (strpos($address, 'nigeria') !== false || strpos($address, 'africa') !== false) {
        return 'Africa';
    } elseif (strpos($address, 'venezuela') !== false || strpos($address, 'brazil') !== false || 
              strpos($address, 'argentina') !== false || strpos($address, 'chile') !== false ||
              strpos($address, 'colombia') !== false || strpos($address, 'peru') !== false) {
        return 'South America';
    } elseif (strpos($address, 'india') !== false || strpos($address, 'china') !== false || 
              strpos($address, 'japan') !== false || strpos($address, 'thailand') !== false ||
              strpos($address, 'singapore') !== false || strpos($address, 'malaysia') !== false) {
        return 'Asia';
    } elseif (strpos($address, 'usa') !== false || strpos($address, 'canada') !== false || 
              strpos($address, 'mexico') !== false || strpos($address, 'united states') !== false) {
        return 'North America';
    } elseif (strpos($address, 'uk') !== false || strpos($address, 'germany') !== false || 
              strpos($address, 'france') !== false || strpos($address, 'italy') !== false ||
              strpos($address, 'spain') !== false || strpos($address, 'europe') !== false) {
        return 'Europe';
    } elseif (strpos($address, 'australia') !== false || strpos($address, 'new zealand') !== false) {
        return 'Oceania';
    }
    
    return 'Unknown';
}

// Get translation service instance
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();

// Auto-translate static text content with caching
function autoTranslate($text) {
    global $translationService, $currentLanguage;
    if ($currentLanguage === 'en') {
        return $text;
    }
    return $translationService->translateText($text, 'en', $currentLanguage) ?? $text;
}

// Define static texts for translation
$texts = [
    'night_thousand_crusades' => 'Night of a Thousand Crusades',
    'global_reach_description' => 'Join us for powerful crusades around the world as we spread the gospel and reach souls in every nation.',
    'loading_regional_events' => 'Loading regional events...',
    'showing_events_from' => 'Showing events from',
    'only' => 'only',
    'showing_all_global_events' => 'Showing all global events',
    'error_loading_events' => 'Error loading events. Please refresh the page.',
    'footer_contact' => 'Contact Us',
    'contact_email' => 'Email',
    'uk' => 'UK',
    'usa' => 'USA',
    'free_phone' => 'Free Phone',
    'footer_copyright' => '© 2025 Rhapsody of Realities. All rights reserved.',
    'site_title' => 'Rhapsody Crusades',
    'date' => 'Date',
    'venue' => 'Venue',
    'register_now' => 'Register Now',
    'past_event' => 'Past Event',
    'km_away' => 'km away',
    'show_more_events' => 'Show More Events from Other Countries',
    'go_to_slide' => 'Go to slide',
    'no_events_country' => 'No events currently scheduled for your country',
    'check_global_events' => 'Check out events happening in other countries around the world',
    'view_global_events' => 'View Global Events'
];

// Auto-translate all texts
$translatedTexts = [];
foreach ($texts as $key => $englishText) {
    $translatedTexts[$key] = autoTranslate($englishText);
}

// Add coordinates and translate each crusade
foreach ($crusades as &$crusade) {
    $crusade['coordinates'] = getCoordinates($crusade['address']);
    $crusade['continent'] = getContinent($crusade['address']);
    
    // Translate crusade content for current language with caching
    $crusade['translated'] = $translationService->translateCrusade($crusade, $currentLanguage);
}

// Group crusades by continent (will be reordered by JavaScript)
$continentEvents = [];
foreach ($crusades as $crusade) {
    $continent = $crusade['continent'];
    if ($continent !== 'Unknown') {
        $continentEvents[$continent][] = $crusade;
    }
}

// Function to format date
function formatDate($date, $time) {
    $dateTime = DateTime::createFromFormat('Y-m-d H:i', $date . ' ' . $time);
    if ($dateTime) {
        return $dateTime->format('F j, Y • H:i');
    }
    return $date . ' • ' . $time;
}

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="<?php echo Language::getCurrentLanguage(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $translatedTexts['night_thousand_crusades']; ?> - <?php echo $translatedTexts['site_title']; ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/webp" href="../assets/images/favicon.webp">
    <link rel="shortcut icon" type="image/webp" href="../assets/images/favicon.webp">
    <link rel="apple-touch-icon" type="image/webp" href="../assets/images/favicon.webp">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Montserrat:wght@300;400;500;600&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#dc2626',
                        primaryDark: '#b91c1c',
                        secondary: '#ffffff',
                    },
                    fontFamily: {
                        'sans': ['Poppins', 'system-ui', 'sans-serif'],
                        'display': ['Playfair Display', 'serif'],
                    }
                }
            }
        }
    </script>
</head>
<body>

<!-- Page Loader -->
<div id="page-loader" class="fixed inset-0 bg-white flex items-center justify-center z-50">
    <div class="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
</div>

<!-- Header -->
<header class="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 z-50">
    <div class="max-w-6xl mx-auto px-6">
        <div class="flex justify-between items-center py-6">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="../index.php" class="flex items-center">
                    <img class="h-10 w-auto" src="../assets/images/logo.webp" alt="<?php echo $translatedTexts['site_title']; ?>">
                </a>
            </div>
            
            <!-- Language Selector -->
            <div class="relative">
                <button type="button" id="language-menu-button" aria-expanded="false" aria-haspopup="true" 
                        class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-primary transition-colors duration-200 focus:outline-none">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="mr-2"><?php echo strtoupper(Language::getCurrentLanguage()); ?></span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="language-menu" role="menu" aria-orientation="vertical" aria-labelledby="language-menu-button" tabindex="-1" 
                     class="hidden origin-top-right absolute right-0 mt-2 w-48 bg-white border border-gray-200 focus:outline-none z-10 max-h-64 overflow-y-auto shadow-lg">
                    <?php 
                    // Get current URL parameters and preserve them
                    $currentParams = $_GET;
                    foreach (Language::getAvailableLanguages() as $code => $name): 
                        $currentParams['lang'] = $code;
                        $langUrl = '?' . http_build_query($currentParams);
                    ?>
                    <a href="<?php echo $langUrl; ?>" role="menuitem" 
                       class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">
                        <img class="h-4 w-4 mr-3" src="../assets/images/flags/<?php echo $code; ?>.svg" alt="<?php echo $name; ?>">
                        <span><?php echo $name; ?></span>
                        <?php if (Language::getCurrentLanguage() === $code): ?>
                        <svg class="w-4 h-4 ml-auto text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <?php endif; ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Main Content (with top padding to compensate for fixed header) -->
<main class="pt-24">

<script>
    // Language selector functionality
    document.addEventListener('DOMContentLoaded', function() {
        const button = document.getElementById('language-menu-button');
        const menu = document.getElementById('language-menu');
        
        // Explicitly set initial state
        menu.style.display = 'none';
        button.setAttribute('aria-expanded', 'false');

        // Toggle menu
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const isHidden = menu.style.display === 'none';
            menu.style.display = isHidden ? 'block' : 'none';
            button.setAttribute('aria-expanded', isHidden ? 'true' : 'false');
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (menu.style.display === 'block' && !menu.contains(event.target)) {
                menu.style.display = 'none';
                button.setAttribute('aria-expanded', 'false');
            }
        });
    });

    // Pastor Crusade Form functionality
    document.addEventListener('DOMContentLoaded', function() {
        const pastorForm = document.getElementById('pastor-crusade-form');
        const zoneSelect = document.getElementById('zone');
        const countrySelect = document.getElementById('country');
        
        if (zoneSelect) {
            // Load zones from JSON file
            fetch('../data/zones.json')
                .then(response => response.json())
                .then(data => {
                    const zones = data.zones;
                    zones.forEach(zone => {
                        const option = document.createElement('option');
                        option.value = zone;
                        option.textContent = zone;
                        zoneSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading zones:', error);
                });
        }
        
        if (countrySelect) {
            // Load countries list
            const countries = [
                'Afghanistan', 'Albania', 'Algeria', 'Andorra', 'Angola', 'Antigua and Barbuda', 'Argentina', 'Armenia', 'Australia', 'Austria',
                'Azerbaijan', 'Bahamas', 'Bahrain', 'Bangladesh', 'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 'Bhutan',
                'Bolivia', 'Bosnia and Herzegovina', 'Botswana', 'Brazil', 'Brunei', 'Bulgaria', 'Burkina Faso', 'Burundi', 'Cambodia', 'Cameroon',
                'Canada', 'Cape Verde', 'Central African Republic', 'Chad', 'Chile', 'China', 'Colombia', 'Comoros', 'Congo', 'Costa Rica',
                'Croatia', 'Cuba', 'Cyprus', 'Czech Republic', 'Democratic Republic of the Congo', 'Denmark', 'Djibouti', 'Dominica', 'Dominican Republic', 'East Timor',
                'Ecuador', 'Egypt', 'El Salvador', 'Equatorial Guinea', 'Eritrea', 'Estonia', 'Ethiopia', 'Fiji', 'Finland', 'France',
                'Gabon', 'Gambia', 'Georgia', 'Germany', 'Ghana', 'Greece', 'Grenada', 'Guatemala', 'Guinea', 'Guinea-Bissau',
                'Guyana', 'Haiti', 'Honduras', 'Hungary', 'Iceland', 'India', 'Indonesia', 'Iran', 'Iraq', 'Ireland',
                'Israel', 'Italy', 'Ivory Coast', 'Jamaica', 'Japan', 'Jordan', 'Kazakhstan', 'Kenya', 'Kiribati', 'Kuwait',
                'Kyrgyzstan', 'Laos', 'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libya', 'Liechtenstein', 'Lithuania', 'Luxembourg',
                'Macedonia', 'Madagascar', 'Malawi', 'Malaysia', 'Maldives', 'Mali', 'Malta', 'Marshall Islands', 'Mauritania', 'Mauritius',
                'Mexico', 'Micronesia', 'Moldova', 'Monaco', 'Mongolia', 'Montenegro', 'Morocco', 'Mozambique', 'Myanmar', 'Namibia',
                'Nauru', 'Nepal', 'Netherlands', 'New Zealand', 'Nicaragua', 'Niger', 'Nigeria', 'North Korea', 'Norway', 'Oman',
                'Pakistan', 'Palau', 'Panama', 'Papua New Guinea', 'Paraguay', 'Peru', 'Philippines', 'Poland', 'Portugal', 'Qatar',
                'Romania', 'Russia', 'Rwanda', 'Saint Kitts and Nevis', 'Saint Lucia', 'Saint Vincent and the Grenadines', 'Samoa', 'San Marino', 'Sao Tome and Principe', 'Saudi Arabia',
                'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapore', 'Slovakia', 'Slovenia', 'Solomon Islands', 'Somalia', 'South Africa',
                'South Korea', 'South Sudan', 'Spain', 'Sri Lanka', 'Sudan', 'Suriname', 'Swaziland', 'Sweden', 'Switzerland', 'Syria',
                'Taiwan', 'Tajikistan', 'Tanzania', 'Thailand', 'Togo', 'Tonga', 'Trinidad and Tobago', 'Tunisia', 'Turkey', 'Turkmenistan',
                'Tuvalu', 'Uganda', 'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States', 'Uruguay', 'Uzbekistan', 'Vanuatu', 'Vatican City',
                'Venezuela', 'Vietnam', 'Yemen', 'Zambia', 'Zimbabwe'
            ];
            
            countries.forEach(country => {
                const option = document.createElement('option');
                option.value = country;
                option.textContent = country;
                countrySelect.appendChild(option);
            });
        }
        
        // Handle form submission
        if (pastorForm) {
            pastorForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Basic form validation
                const requiredFields = pastorForm.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('border-red-500');
                    } else {
                        field.classList.remove('border-red-500');
                    }
                });
                
                if (isValid) {
                    // Show loading state
                    const submitBtn = pastorForm.querySelector('button[type="submit"]');
                    const originalText = submitBtn.textContent;
                    submitBtn.textContent = 'Submitting...';
                    submitBtn.disabled = true;
                    
                    // Submit form
                    const formData = new FormData(pastorForm);
                    
                    fetch('pastor-crusade-request-handler.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Thank you for your crusade request! We have received your submission and our team will review it. You will be contacted within 5-7 business days.');
                            pastorForm.reset();
                        } else {
                            alert('There was an error submitting your request: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('There was an error submitting your request. Please try again.');
                    })
                    .finally(() => {
                        submitBtn.textContent = originalText;
                        submitBtn.disabled = false;
                    });
                } else {
                    alert('Please fill in all required fields.');
                }
            });
        }
    });

    // Page loader
    window.addEventListener('load', function() {
        document.getElementById('page-loader').style.display = 'none';
        document.body.classList.add('loaded');
    });
</script>
<?php
?>

<!-- Night of a Thousand Crusades Section -->
<section class="pt-24 pb-8 bg-white">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-12">
            <h1 class="text-4xl lg:text-5xl font-light text-gray-900 mb-6"><?php echo $translatedTexts['night_thousand_crusades']; ?></h1>
            <p class="text-xl text-gray-600 mb-6"><?php echo $translatedTexts['global_reach_description']; ?></p>
            <div class="w-16 h-px bg-primary mx-auto"></div>
            
            <!-- Events Status -->
            <div id="events-status" class="mt-6 p-3 bg-gray-50 rounded-lg text-sm text-gray-600">
                <div id="events-info" class="text-blue-600">
                    <i class="fas fa-globe mr-2"></i>
                    <span id="region-text"><?php echo $translatedTexts['loading_regional_events']; ?></span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pastor Crusade Registration Section -->
<section class="pt-8 pb-16 bg-white">
    <div class="max-w-4xl mx-auto px-6">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-light text-gray-900 mb-6"><?php echo autoTranslate('Host Your Own Crusade'); ?></h2>
            <p class="text-lg text-gray-600 mb-6"><?php echo autoTranslate('Are you a pastor who wants to host a crusade in your area? Fill out this form to request a crusade and our team will get in touch with you.'); ?></p>
            <div class="w-16 h-px bg-primary mx-auto"></div>
        </div>

        <!-- Registration Form -->
        <div class="bg-gray-50 border border-gray-200 p-8 lg:p-12">
            <form id="pastor-crusade-form" method="POST" action="pastor-crusade-request-handler.php">
                
                <!-- Personal Information Section -->
                <div class="mb-12">
                    <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo autoTranslate('Personal Information'); ?></h3>
                    
                    <!-- Designation First -->
                    <div class="mb-6">
                        <label for="designation" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Designation'); ?> *</label>
                        <select id="designation" name="designation" required
                                class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                            <option value=""><?php echo autoTranslate('Select your designation'); ?></option>
                            <option value="pastor"><?php echo autoTranslate('Pastor'); ?></option>
                            <option value="dr"><?php echo autoTranslate('Dr'); ?></option>
                            <option value="rev"><?php echo autoTranslate('Rev'); ?></option>
                            <option value="bishop"><?php echo autoTranslate('Bishop'); ?></option>
                            <option value="elder"><?php echo autoTranslate('Elder'); ?></option>
                        </select>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('First Name'); ?> *</label>
                            <input type="text" id="first_name" name="first_name" required
                                   class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                                   placeholder="<?php echo autoTranslate('Enter your first name'); ?>">
                        </div>
                        
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Last Name'); ?> *</label>
                            <input type="text" id="last_name" name="last_name" required
                                   class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                                   placeholder="<?php echo autoTranslate('Enter your last name'); ?>">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Email Address'); ?> *</label>
                            <input type="email" id="email" name="email" required
                                   class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                                   placeholder="<?php echo autoTranslate('Enter your email address'); ?>">
                        </div>
                        
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Phone Number'); ?> *</label>
                            <input type="tel" id="phone" name="phone" required
                                   class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                                   placeholder="<?php echo autoTranslate('Enter your phone number'); ?>">
                        </div>
                    </div>

                    <div class="mt-6">
                        <label for="kingschat_username" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('KingsChat Username'); ?> *</label>
                        <input type="text" id="kingschat_username" name="kingschat_username" required
                               class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                               placeholder="<?php echo autoTranslate('Enter your KingsChat username'); ?>">
                    </div>
                </div>

                <!-- Church Information Section -->
                <div class="mb-12">
                    <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo autoTranslate('Church Information'); ?></h3>
                    
                    <!-- Zone Dropdown -->
                    <div class="mb-6">
                        <label for="zone" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Zone'); ?> *</label>
                        <select id="zone" name="zone" required
                                class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                            <option value=""><?php echo autoTranslate('Select your zone'); ?></option>
                            <!-- Zone options will be populated from JSON file -->
                        </select>
                    </div>
                    
                    <!-- Group and Church -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="group" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Group'); ?> *</label>
                            <input type="text" id="group" name="group" required
                                   class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                                   placeholder="<?php echo autoTranslate('Enter your group name'); ?>">
                        </div>
                        
                        <div>
                            <label for="church" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Church'); ?> *</label>
                            <input type="text" id="church" name="church" required
                                   class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                                   placeholder="<?php echo autoTranslate('Enter your church name'); ?>">
                        </div>
                    </div>
                </div>

                <!-- Crusade Information Section -->
                <div class="mb-12">
                    <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo autoTranslate('Proposed Crusade Details'); ?></h3>
                    
                    <div class="mb-6">
                        <label for="crusade_title" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Crusade Title'); ?> *</label>
                        <input type="text" id="crusade_title" name="crusade_title" required
                               class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                               placeholder="<?php echo autoTranslate('Enter the proposed crusade title'); ?>">
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="country" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Country'); ?> *</label>
                            <select id="country" name="country" required
                                    class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                                <option value=""><?php echo autoTranslate('Select country'); ?></option>
                                <!-- Countries will be populated via JavaScript -->
                            </select>
                        </div>
                        
                        <div>
                            <label for="city" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('City/Location'); ?> *</label>
                            <input type="text" id="city" name="city" required
                                   class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                                   placeholder="<?php echo autoTranslate('Enter city or specific location'); ?>">
                        </div>
                    </div>

                    <div class="mt-6">
                        <label for="venue" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Proposed Venue'); ?> *</label>
                        <input type="text" id="venue" name="venue" required
                               class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                               placeholder="<?php echo autoTranslate('Enter proposed venue (stadium, auditorium, etc.)'); ?>">
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                        <div>
                            <label for="preferred_date" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Preferred Date'); ?> *</label>
                            <input type="date" id="preferred_date" name="preferred_date" required
                                   class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                        </div>
                        
                        <div>
                            <label for="preferred_time" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Preferred Time'); ?> *</label>
                            <input type="time" id="preferred_time" name="preferred_time" required
                                   class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                        </div>
                        
                        <div>
                            <label for="expected_attendance" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Expected Attendance'); ?> *</label>
                            <select id="expected_attendance" name="expected_attendance" required
                                    class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                                <option value=""><?php echo autoTranslate('Select expected attendance'); ?></option>
                                <option value="100-500">100 - 500</option>
                                <option value="500-1000">500 - 1,000</option>
                                <option value="1000-5000">1,000 - 5,000</option>
                                <option value="5000-10000">5,000 - 10,000</option>
                                <option value="10000-25000">10,000 - 25,000</option>
                                <option value="25000+">25,000+</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="mb-12">
                    <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo autoTranslate('Additional Information'); ?></h3>
                    
                    <div>
                        <label for="additional_comments" class="block text-sm font-medium text-gray-700 mb-2"><?php echo autoTranslate('Additional Comments or Special Requests'); ?></label>
                        <textarea id="additional_comments" name="additional_comments" rows="4"
                                  class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 resize-none"
                                  placeholder="<?php echo autoTranslate('Any additional information, special requirements, or comments about your crusade request'); ?>"></textarea>
                    </div>
                </div>

                <!-- Submit Section -->
                <div class="border-t border-gray-200 pt-8">
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button type="submit" 
                                class="px-8 py-4 bg-primary text-white font-medium hover:bg-red-700 transition-colors duration-200">
                            <?php echo autoTranslate('Submit Crusade Request'); ?>
                        </button>
                        
                        <button type="reset" 
                                class="px-8 py-4 border border-gray-300 text-gray-700 font-medium hover:border-gray-400 hover:bg-gray-50 transition-colors duration-200">
                            <?php echo autoTranslate('Clear Form'); ?>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- World Crusades Section -->
<section class="py-12 bg-gray-50">
    <div class="max-w-6xl mx-auto px-6">
        <div class="relative">
            <!-- Dynamic Carousel Container (will be populated via API) -->
            <div id="crusadesCarousel" class="overflow-hidden relative px-8 sm:px-12 lg:px-20">
                <!-- Loading state -->
                <div id="carousel-loading" class="flex justify-center items-center py-16">
                    <div class="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Enhanced carousel functionality
function goToSlide(index) {
    const slider = document.getElementById('crusadesSlider');
    const dots = document.querySelectorAll('#dotsContainer button');

    if (slider) {
        // Update global current index
        currentIndex = index;
        
        slider.style.transform = `translateX(-${index * 100}%)`;
        slider.style.transition = 'transform 0.5s ease-in-out';

        // Update active dot
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.classList.add('bg-primary');
                dot.classList.remove('bg-gray-300');
            } else {
                dot.classList.remove('bg-primary');
                dot.classList.add('bg-gray-300');
            }
        });
    }
}

// Crusades data from PHP
const crusadesData = <?= json_encode($crusades) ?>;

// Function to calculate distance between two coordinates (Haversine formula)
function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
             Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
             Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c; // Distance in kilometers
}

// Function to reorder crusades by proximity to user location
function reorderCrusadesByProximity(userLat, userLng) {
    console.log('User location:', userLat, userLng);
    
    // Calculate distances for each crusade
    const crusadeDistances = [];
    crusadesData.forEach((crusade, index) => {
        if (crusade.coordinates && crusade.coordinates.lat !== 0) {
            const distance = calculateDistance(
                userLat, userLng,
                crusade.coordinates.lat, crusade.coordinates.lng
            );
            crusadeDistances.push({ crusade, distance, originalIndex: index });
            console.log(`${crusade.title}: ${distance.toFixed(0)} km`);
        } else {
            crusadeDistances.push({ crusade, distance: Infinity, originalIndex: index });
        }
    });
    
    // Sort by distance
    crusadeDistances.sort((a, b) => a.distance - b.distance);
    
    // Reorder slides in the DOM
    const slidesContainer = document.querySelector('#crusadesSlider');
    const slides = Array.from(document.querySelectorAll('#crusadesSlider > div'));
    
    // Clear container
    slidesContainer.innerHTML = '';
    
    // Add slides in order of proximity
    crusadeDistances.forEach(({ crusade, distance, originalIndex }) => {
        const slide = slides[originalIndex];
        if (slide) {
            slidesContainer.appendChild(slide);
            
            // Add distance indicator to the slide
            if (distance !== Infinity && distance < 20000) { // Only show if less than 20,000km
                const distanceElement = document.createElement('div');
                distanceElement.className = 'absolute top-4 left-4 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md';
                distanceElement.textContent = `${Math.round(distance)} km away`;
                slide.querySelector('.relative').appendChild(distanceElement);
            }
        }
    });
    
    // Update dots container
    const dotsContainer = document.getElementById('dotsContainer');
    dotsContainer.innerHTML = '';
    crusadeDistances.forEach((item, index) => {
        const button = document.createElement('button');
        button.onclick = () => goToSlide(index);
        button.setAttribute('aria-label', `<?php echo $translatedTexts['go_to_slide']; ?> ${index + 1}`);
        button.setAttribute('data-index', index);
        button.className = `w-3 h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 ${index === 0 ? 'bg-primary' : ''}`;
        dotsContainer.appendChild(button);
    });
    
    // Reset and reinitialize carousel with reordered slides
    resetCarousel();
    setTimeout(() => initializeCarousel(), 100);
}

// Simple geocoding for major cities
const cityCoordinates = {
    // Nigeria
    'lagos': { lat: 6.5244, lng: 3.3792, country: 'Nigeria' },
    'abuja': { lat: 9.0579, lng: 7.4951, country: 'Nigeria' },
    
    // Venezuela
    'caracas': { lat: 10.4806, lng: -66.9036, country: 'Venezuela' },
    'valencia': { lat: 10.1620, lng: -68.0075, country: 'Venezuela' },
    
    // India
    'delhi': { lat: 28.7041, lng: 77.1025, country: 'India' },
    'mumbai': { lat: 19.0760, lng: 72.8777, country: 'India' },
    'bangalore': { lat: 12.9716, lng: 77.5946, country: 'India' },
    'chennai': { lat: 13.0827, lng: 80.2707, country: 'India' },
    
    // Brazil
    'são paulo': { lat: -23.5505, lng: -46.6333, country: 'Brazil' },
    'sao paulo': { lat: -23.5505, lng: -46.6333, country: 'Brazil' },
    'rio de janeiro': { lat: -22.9068, lng: -43.1729, country: 'Brazil' },
    
    // Kenya
    'nairobi': { lat: -1.2921, lng: 36.8219, country: 'Kenya' },
    'mombasa': { lat: -4.0435, lng: 39.6682, country: 'Kenya' },
    
    // USA
    'new york': { lat: 40.7128, lng: -74.0060, country: 'USA' },
    'los angeles': { lat: 34.0522, lng: -118.2437, country: 'USA' },
    'chicago': { lat: 41.8781, lng: -87.6298, country: 'USA' },
    
    // UK
    'london': { lat: 51.5074, lng: -0.1278, country: 'UK' },
    'manchester': { lat: 53.4808, lng: -2.2426, country: 'UK' },
    'birmingham': { lat: 52.4862, lng: -1.8904, country: 'UK' },
    
    // Other major cities
    'paris': { lat: 48.8566, lng: 2.3522, country: 'France' },
    'berlin': { lat: 52.5200, lng: 13.4050, country: 'Germany' },
    'madrid': { lat: 40.4168, lng: -3.7038, country: 'Spain' },
    'rome': { lat: 41.9028, lng: 12.4964, country: 'Italy' },
    'tokyo': { lat: 35.6762, lng: 139.6503, country: 'Japan' },
    'sydney': { lat: -33.8688, lng: 151.2093, country: 'Australia' },
    'toronto': { lat: 43.6532, lng: -79.3832, country: 'Canada' },
    'dubai': { lat: 25.2048, lng: 55.2708, country: 'UAE' },
    'singapore': { lat: 1.3521, lng: 103.8198, country: 'Singapore' },
    'johannesburg': { lat: -26.2041, lng: 28.0473, country: 'South Africa' },
    'accra': { lat: 5.6037, lng: -0.1870, country: 'Ghana' },
    'cairo': { lat: 30.0444, lng: 31.2357, country: 'Egypt' },
    'moscow': { lat: 55.7558, lng: 37.6176, country: 'Russia' },
    'beijing': { lat: 39.9042, lng: 116.4074, country: 'China' }
};

// Function to search for city coordinates
function searchCityLocation(cityName) {
    const normalizedCity = cityName.toLowerCase().trim();
    return cityCoordinates[normalizedCity] || null;
}

// Function to search countries using the API
async function searchCountryAPI(query) {
    try {
        const response = await fetch(`../api/country-search.php?q=${encodeURIComponent(query)}`);
        const data = await response.json();
        return data.results || [];
    } catch (error) {
        console.error('Country search error:', error);
        return [];
    }
}

// Function to get user's location and reorder crusades
function getUserLocationAndReorder() {
    const gettingLocation = document.getElementById('getting-location');
    const locationFound = document.getElementById('location-found');
    const locationError = document.getElementById('location-error');
    
    if (navigator.geolocation) {
        console.log('Getting user location...');
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const userLat = position.coords.latitude;
                const userLng = position.coords.longitude;
                
                // Hide getting location, show found
                gettingLocation.classList.add('hidden');
                locationFound.classList.remove('hidden');
                document.getElementById('location-text').textContent = 
                    `${userLat.toFixed(2)}, ${userLng.toFixed(2)}`;
                
                reorderCrusadesByProximity(userLat, userLng);
            },
            function(error) {
                console.log('Geolocation error:', error.message);
                showLocationError();
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000 // 5 minutes
            }
        );
    } else {
        console.log('Geolocation not supported');
        showLocationError();
    }
}

// Function to show location error and setup manual input
function showLocationError() {
    const gettingLocation = document.getElementById('getting-location');
    const locationError = document.getElementById('location-error');
    
    gettingLocation.classList.add('hidden');
    locationError.classList.remove('hidden');
    
    // Setup manual location button
    const manualBtn = document.getElementById('manual-location-btn');
    const manualLocation = document.getElementById('manual-location');
    const searchBtn = document.getElementById('search-location-btn');
    const cityInput = document.getElementById('manual-city');
    
    manualBtn.addEventListener('click', function() {
        manualLocation.classList.remove('hidden');
        cityInput.focus();
    });
    
    searchBtn.addEventListener('click', function() {
        const cityName = cityInput.value.trim();
        if (cityName) {
            const coords = searchCityLocation(cityName);
            if (coords) {
                // Hide error, show found
                locationError.classList.add('hidden');
                document.getElementById('location-found').classList.remove('hidden');
                document.getElementById('location-text').textContent = 
                    `${cityName}, ${coords.country}`;
                
                reorderCrusadesByProximity(coords.lat, coords.lng);
            } else {
                alert('City not found. Try: Lagos, London, New York, Paris, Tokyo, Delhi, etc.');
            }
        }
    });
    
    // Allow Enter key to search
    cityInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchBtn.click();
        }
    });
    
    // Fallback: initialize without reordering
    initializeCarousel();
}

// Show no events message
function showNoEventsMessage(country, hasGlobalEvents) {
    const carousel = document.getElementById('crusadesCarousel');
    const existingShowMore = document.getElementById('show-more-container');
    if (existingShowMore) existingShowMore.remove();
    
    carousel.innerHTML = `
        <div class="flex items-center justify-center min-h-[400px] bg-gray-50 border border-gray-200">
            <div class="text-center px-8 py-12 max-w-md mx-auto">
                <div class="w-20 h-20 mx-auto mb-6 bg-gray-200 flex items-center justify-center">
                    <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-light text-gray-900 mb-4"><?php echo $translatedTexts['no_events_country']; ?></h3>
                <p class="text-gray-600 mb-6"><?php echo $translatedTexts['check_global_events']; ?></p>
                ${hasGlobalEvents ? `
                    <button id="view-global-events-btn" class="bg-primary text-white px-6 py-3 hover:bg-red-700 transition-colors font-medium">
                        <i class="fas fa-globe mr-2"></i><?php echo $translatedTexts['view_global_events']; ?>
                    </button>
                ` : ''}
            </div>
        </div>
    `;
    
    // Add click handler for global events button
    if (hasGlobalEvents) {
        setTimeout(() => {
            const btn = document.getElementById('view-global-events-btn');
            if (btn) {
                btn.addEventListener('click', async function() {
                    // Show loading state
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
                    btn.disabled = true;
                    
                    try {
                        const allResponse = await fetch('../crusadesjson.php?show_all=true');
                        const allData = await allResponse.json();
                        
                        await renderAllEvents(allData);
                    } catch (error) {
                        console.error('Error loading all events:', error);
                        btn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Error loading events';
                        btn.disabled = false;
                    }
                });
            }
        }, 100);
    }
}

// Fetch events from API and render with regional filtering
async function fetchAndRenderEvents() {
    try {
        const response = await fetch('../crusadesjson.php');
        const data = await response.json();
        

        
        // Update region text
        const regionText = document.getElementById('region-text');
        if (data.user_country && data.user_country !== 'Unknown') {
            if (data.show_all) {
                regionText.textContent = `<?php echo $translatedTexts['showing_all_global_events']; ?>`;
            } else {
                regionText.textContent = `<?php echo $translatedTexts['showing_events_from']; ?> ${data.user_country} <?php echo $translatedTexts['only']; ?>`;
            }
        } else {
            regionText.textContent = '<?php echo $translatedTexts['showing_all_global_events']; ?>';
        }
        
        // Clear existing content
        const carousel = document.getElementById('crusadesCarousel');
        const existingShowMore = document.getElementById('show-more-container');
        if (existingShowMore) existingShowMore.remove();
        
        let eventsToShow = [];
        
        // If showing all, use all_events
        if (data.show_all && data.all_events) {
            eventsToShow = data.all_events;
        } else {
            // Show only user country events initially
            if (data.user_country_events && data.user_country_events.length > 0) {
                eventsToShow = data.user_country_events;
            } else if (data.has_country_events === false && data.user_country) {
                // No events in user's country - show no events message
                showNoEventsMessage(data.user_country, data.has_more_events);
                return;
            }
        }
        
        // If we still have no events to show, show no events message
        if (!eventsToShow || eventsToShow.length === 0) {
            showNoEventsMessage(data.user_country || 'your area', data.has_more_events);
            return;
        }
        
        // Create carousel structure
        carousel.innerHTML = `
            <div id="crusadesSlider" class="flex transition-transform duration-500 ease-in-out">
                ${eventsToShow.map((crusade, index) => createCrusadeSlide(crusade, index, userLocation)).join('')}
            </div>
            <button id="prevButton" class="absolute top-1/2 left-2 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-lg">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button id="nextButton" class="absolute top-1/2 right-2 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-lg">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
            <div id="dotsContainer" class="flex justify-center mt-16 space-x-4">
                ${eventsToShow.map((_, index) => `
                    <button onclick="goToSlide(${index})" aria-label="<?php echo $translatedTexts['go_to_slide']; ?> ${index + 1}" data-index="${index}" class="w-3 h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 ${index === 0 ? 'bg-primary' : ''}"></button>
                `).join('')}
            </div>
        `;
        
        // Show more button if we have events from other countries and not showing all
        if (data.has_more_events && !data.show_all) {
            const showMoreContainer = document.createElement('div');
            showMoreContainer.id = 'show-more-container';
            showMoreContainer.className = 'text-center mt-8 px-6';
            const moreCount = data.total_other_events;
            
            showMoreContainer.innerHTML = `
                <div class="bg-gray-50 border border-gray-200 p-6">
                    <p class="text-gray-600 mb-4">Found ${moreCount} more events in other countries</p>
                    <button id="show-more-button" class="px-6 py-3 bg-primary text-white hover:bg-red-700 transition-colors">
                        <i class="fas fa-globe mr-2"></i><?php echo $translatedTexts['show_more_events']; ?>
                    </button>
                </div>
            `;
            
            // Find the carousel section and append after it
            const carouselSection = carousel.closest('section');
            if (carouselSection) {
                // Insert after the carousel section
                carouselSection.parentNode.insertBefore(showMoreContainer, carouselSection.nextSibling);
            } else {
                // Fallback: append to the body or main content area
                const mainContent = document.querySelector('main');
                if (mainContent) {
                    mainContent.appendChild(showMoreContainer);
                } else {
                    carousel.parentElement.appendChild(showMoreContainer);
                }
            }
            
            // Add click handler for show more
            document.getElementById('show-more-button').addEventListener('click', async function() {
                // Show loading state
                const button = document.getElementById('show-more-button');
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading more events...';
                button.disabled = true;
                
                try {
                    const allResponse = await fetch('../crusadesjson.php?show_all=true');
                    const allData = await allResponse.json();
                    
                    // Update the data to show all events
                    await renderAllEvents(allData);
                    
                    // Remove show more button
                    showMoreContainer.remove();
                } catch (error) {
                    console.error('Error loading more events:', error);
                    button.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Error loading events';
                    button.disabled = false;
                }
            });
        }
        
        // Initialize carousel controls
        resetCarousel();
        setTimeout(() => initializeCarousel(), 100);
        
    } catch (error) {
        console.error('Error fetching events:', error);
        const regionText = document.getElementById('region-text');
        regionText.textContent = '<?php echo $translatedTexts['error_loading_events']; ?>';
        regionText.className = 'text-red-600';
    }
}

// Render all events when "Show More" is clicked
async function renderAllEvents(data) {
    const carousel = document.getElementById('crusadesCarousel');
    const regionText = document.getElementById('region-text');
    
    regionText.textContent = '<?php echo $translatedTexts['showing_all_global_events']; ?>';
    
    const allEvents = data.all_events || [];
    
    carousel.innerHTML = `
        <div id="crusadesSlider" class="flex transition-transform duration-500 ease-in-out">
            ${allEvents.map((crusade, index) => createCrusadeSlide(crusade, index, userLocation)).join('')}
        </div>
        <button id="prevButton" class="absolute top-1/2 left-2 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-lg">
            <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>
        <button id="nextButton" class="absolute top-1/2 right-2 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-lg">
            <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>
        <div id="dotsContainer" class="flex justify-center mt-16 space-x-4">
            ${allEvents.map((_, index) => `
                <button onclick="goToSlide(${index})" aria-label="Go to slide ${index + 1}" data-index="${index}" class="w-3 h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 ${index === 0 ? 'bg-primary' : ''}"></button>
            `).join('')}
        </div>
    `;
    
    // Reset and reinitialize carousel
    resetCarousel();
    setTimeout(() => initializeCarousel(), 100);
}

function createCrusadeSlide(crusade, index, userLocation = null) {
    const eventDate = new Date(crusade.date + ' ' + crusade.time);
    const currentDate = new Date();
    const isPastEvent = eventDate < currentDate;
    const eventClass = isPastEvent ? 'opacity-60' : '';
    
    // Use translated content if available, otherwise fall back to original
    const title = crusade.translated?.title || crusade.title;
    const description = crusade.translated?.description || crusade.description;
    const venue = crusade.translated?.venue || crusade.venue;
    
    // Remove distance calculation
    let distanceBadge = '';
    let distanceInfo = '';
    
    return `
        <div class="w-full flex-shrink-0 px-4 sm:px-6">
            <div class="bg-white border border-gray-200 overflow-hidden ${eventClass}">
                <div class="relative flex flex-col lg:flex-row">
                    ${isPastEvent ? '<div class="absolute top-4 right-4 bg-red-600 text-white text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md"><?php echo $translatedTexts['past_event']; ?></div>' : ''}
                    ${distanceBadge}
                    ${crusade.country && crusade.country !== 'Unknown' ? `<div class="absolute top-4 left-4 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md">${crusade.country}</div>` : ''}
                    
                    <div class="w-full lg:w-1/2 relative">
                        <div class="aspect-[16/9] sm:aspect-[4/3] lg:aspect-auto lg:h-full overflow-hidden">
                            <img class="w-full h-full object-cover" src="../${crusade.image}" alt="${title}">
                        </div>
                    </div>
                    
                    <div class="w-full lg:w-1/2 p-6 sm:p-8 lg:p-12 flex flex-col justify-center">
                        <div class="space-y-6 lg:space-y-8">
                            <h3 class="text-2xl sm:text-3xl lg:text-4xl font-light text-gray-900 leading-tight">${title}</h3>
                            <p class="text-base sm:text-lg text-gray-600 leading-relaxed">${description}</p>
                            
                            <div class="space-y-3 sm:space-y-4">
                                <div class="flex items-start sm:items-center">
                                    <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <div class="flex flex-col sm:flex-row sm:items-center">
                                        <span class="text-sm font-medium text-gray-500 sm:w-20 sm:mr-2"><?php echo $translatedTexts['date']; ?>:</span>
                                        <span class="text-sm sm:text-base text-gray-900 font-medium">
                                            ${eventDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })} at ${eventDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex items-start sm:items-center">
                                    <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <div class="flex flex-col">
                                        <span class="text-sm font-medium text-gray-500"><?php echo $translatedTexts['venue']; ?></span>
                                        <span class="text-sm sm:text-base text-gray-900 font-medium">
                                            ${venue}<br>
                                            ${crusade.address}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            ${!isPastEvent ? `
                            <div class="pt-2 flex flex-col sm:flex-row gap-4">
                                <a href="../${crusade.register_link}" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                                    <?php echo $translatedTexts['register_now']; ?>
                                    <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Function to initialize carousel
// Global carousel state
let currentIndex = 0;
let carouselInterval = null;
let isCarouselInitialized = false;

function initializeCarousel() {
    // Prevent multiple initializations
    if (isCarouselInitialized) {
        return;
    }
    
    const prevButton = document.getElementById('prevButton');
    const nextButton = document.getElementById('nextButton');
    const slider = document.getElementById('crusadesSlider');
    const dots = document.querySelectorAll('#dotsContainer button');

    if (prevButton && nextButton && slider) {
        const slides = slider.children.length;
        currentIndex = 0;

        // Clear any existing interval
        if (carouselInterval) {
            clearInterval(carouselInterval);
        }

        // Initialize first dot as active
        if (dots.length > 0) {
            dots[0].classList.add('bg-primary');
            dots[0].classList.remove('bg-gray-300');
        }

        // Remove existing event listeners by cloning and replacing elements
        const newPrevButton = prevButton.cloneNode(true);
        const newNextButton = nextButton.cloneNode(true);
        prevButton.parentNode.replaceChild(newPrevButton, prevButton);
        nextButton.parentNode.replaceChild(newNextButton, nextButton);

        // Add fresh event listeners
        newPrevButton.addEventListener('click', function() {
            currentIndex = (currentIndex - 1 + slides) % slides;
            goToSlide(currentIndex);
        });

        newNextButton.addEventListener('click', function() {
            currentIndex = (currentIndex + 1) % slides;
            goToSlide(currentIndex);
        });

        // Auto-advance disabled - manual navigation only
        
        isCarouselInitialized = true;
    }
}

// Function to reset carousel state
function resetCarousel() {
    if (carouselInterval) {
        clearInterval(carouselInterval);
        carouselInterval = null;
    }
    isCarouselInitialized = false;
    currentIndex = 0;
}

// Global variable to store user location for distance calculations
let userLocation = null;

// Try to get user location for distance calculations
function getUserLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                userLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                console.log('User location obtained for distance calculations:', userLocation);
                // Re-render events with distance info
                fetchAndRenderEvents();
            },
            function(error) {
                console.log('Geolocation not available, showing events without distance');
                // Proceed without location
                fetchAndRenderEvents();
            },
            {
                enableHighAccuracy: false,
                timeout: 5000,
                maximumAge: 300000
            }
        );
    } else {
        console.log('Geolocation not supported');
        fetchAndRenderEvents();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Try to get user location, then fetch events
    getUserLocation();
});

</script>

</main>

<!-- Footer -->
<footer class="bg-white border-t border-gray-200 py-16">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center space-y-12">
            <!-- Contact Information -->
            <div>
                <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo $translatedTexts['footer_contact']; ?></h3>
                <div class="space-y-8">
                    <!-- Email -->
                    <div>
                        <p class="text-sm font-medium text-gray-500 mb-2"><?php echo $translatedTexts['contact_email']; ?></p>
                        <a href="mailto:<EMAIL>" class="text-primary font-medium hover:text-red-700 transition-colors duration-200">
                            <EMAIL>
                        </a>
                    </div>

                    <!-- Phone Numbers Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto">
                        <!-- UK Phone Numbers -->
                        <div class="space-y-3">
                            <p class="text-sm font-medium text-gray-900"><?php echo $translatedTexts['uk']; ?></p>
                            <div class="space-y-1">
                                <p class="text-gray-600">+44(170)855 6604</p>
                                <p class="text-gray-600"><?php echo $translatedTexts['free_phone']; ?>: +44**************</p>
                            </div>
                        </div>

                        <!-- USA Phone Numbers -->
                        <div class="space-y-3">
                            <p class="text-sm font-medium text-gray-900"><?php echo $translatedTexts['usa']; ?></p>
                            <div class="space-y-1">
                                <p class="text-gray-600"><?php echo $translatedTexts['free_phone']; ?>: +1(800)620 8522</p>
                                <p class="text-gray-600"><?php echo $translatedTexts['free_phone']; ?>: +1**************</p>
                                <p class="text-gray-600">+1**************</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="pt-8 border-t border-gray-100">
                <p class="text-sm text-gray-500"><?php echo $translatedTexts['footer_copyright']; ?></p>
            </div>
        </div>
    </div>
</footer>

<!-- Scroll to top button -->
<button id="scroll-to-top" class="fixed bottom-6 right-6 h-12 w-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary flex items-center justify-center opacity-0 invisible transition-all duration-300 focus:outline-none">
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
</button>

<script>
// Scroll to top functionality
const scrollButton = document.getElementById('scroll-to-top');

window.addEventListener('scroll', function() {
    if (window.pageYOffset > 100) {
        scrollButton.classList.remove('opacity-0', 'invisible');
        scrollButton.classList.add('opacity-100', 'visible');
    } else {
        scrollButton.classList.remove('opacity-100', 'visible');
        scrollButton.classList.add('opacity-0', 'invisible');
    }
});

scrollButton.addEventListener('click', function() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});
</script>

</body>
</html>