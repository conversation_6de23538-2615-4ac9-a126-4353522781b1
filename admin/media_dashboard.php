<?php
// Media Dashboard - included in crusades.php

// Load media from JSON file
$mediaJsonFile = __DIR__ . '/../data/media.json';
$categoriesJsonFile = __DIR__ . '/../data/categories.json';

function loadMediaData($file) {
    if (!file_exists($file)) {
        return ['media' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true);
}

function loadCategoriesData($file) {
    if (!file_exists($file)) {
        return ['categories' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true);
}

$mediaData = loadMediaData($mediaJsonFile);
$categoriesData = loadCategoriesData($categoriesJsonFile);
?>

<!-- Category Management Section -->
<div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h2 class="text-xl font-semibold mb-4">Manage Categories</h2>
    
    <!-- Add New Category Form -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 class="text-lg font-medium mb-3">Add New Category</h3>
        <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>?tab=media" method="POST" class="flex gap-4 items-end">
            <input type="hidden" name="action" value="add_category">
            
            <div class="flex-1">
                <label for="category_display_name" class="block text-sm font-medium text-gray-700">Category Name</label>
                <input type="text" id="category_display_name" name="display_name" required 
                       class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                       placeholder="e.g., Special Events">
            </div>
            
            <div class="flex-1">
                <label for="category_description" class="block text-sm font-medium text-gray-700">Description</label>
                <input type="text" id="category_description" name="description" 
                       class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                       placeholder="Category description">
            </div>
            
            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="fas fa-plus mr-2"></i>
                Add Category
            </button>
        </form>
    </div>
    
    <!-- Current Categories List -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php if (empty($categoriesData['categories'])): ?>
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">No categories found.</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($categoriesData['categories'] as $category): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($category['display_name']); ?></div>
                                <div class="text-sm text-gray-500"><?php echo htmlspecialchars($category['name']); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($category['description']); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo date('M j, Y', strtotime($category['created_at'])); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>?tab=media" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this category? This may affect existing media items.');">
                                    <input type="hidden" name="action" value="delete_category">
                                    <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                    <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                                        <i class="fas fa-trash mr-1.5"></i> Delete
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Add New Media Form -->
<div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h2 class="text-xl font-semibold mb-4">Add New Media</h2>
    <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>?tab=media" method="POST" class="space-y-4">
        <input type="hidden" name="action" value="add_media">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="media_title" class="block text-sm font-medium text-gray-700">Title</label>
                <input type="text" id="media_title" name="title" required 
                       class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
            </div>
            
            <div>
                <label for="media_category" class="block text-sm font-medium text-gray-700">Category</label>
                <select id="media_category" name="category" required 
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                    <option value="">Select Category</option>
                    <?php foreach ($categoriesData['categories'] as $category): ?>
                        <option value="<?php echo htmlspecialchars($category['name']); ?>">
                            <?php echo htmlspecialchars($category['display_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="md:col-span-2">
                <label for="media_description" class="block text-sm font-medium text-gray-700">Description</label>
                <textarea id="media_description" name="description" rows="3" required
                         class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"></textarea>
            </div>
            
            <div>
                <label for="media_video_url" class="block text-sm font-medium text-gray-700">Video URL</label>
                <input type="url" id="media_video_url" name="video_url" required 
                       class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                       placeholder="https://www.youtube.com/embed/...">
                <p class="mt-1 text-xs text-gray-500">Use YouTube embed URL format</p>
            </div>
            
            <div>
                <label for="media_thumbnail" class="block text-sm font-medium text-gray-700">Thumbnail URL</label>
                <input type="url" id="media_thumbnail" name="thumbnail" 
                       class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                       placeholder="https://img.youtube.com/vi/VIDEO_ID/maxresdefault.jpg">
                <p class="mt-1 text-xs text-gray-500">YouTube thumbnail URL (optional)</p>
            </div>
            
            <div>
                <label for="media_date_added" class="block text-sm font-medium text-gray-700">Date Added</label>
                <input type="date" id="media_date_added" name="date_added" required 
                       value="<?php echo date('Y-m-d'); ?>"
                       class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="media_featured" name="featured" value="1" 
                       class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                <label for="media_featured" class="ml-2 block text-sm text-gray-900">
                    Featured Video
                </label>
            </div>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="fas fa-plus mr-2"></i>
                Add Media
            </button>
        </div>
    </form>
</div>

<!-- Current Media List -->
<div class="bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold mb-4">Current Media</h2>
    <?php if (empty($mediaData['media'])): ?>
        <p class="text-gray-500">No media found.</p>
    <?php else: ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Video</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($mediaData['media'] as $media): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-20 h-12 rounded overflow-hidden">
                                    <?php if (!empty($media['thumbnail'])): ?>
                                        <img src="<?php echo htmlspecialchars($media['thumbnail']); ?>" alt="Thumbnail" class="w-full h-full object-cover">
                                    <?php else: ?>
                                        <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                            <i class="fas fa-video text-gray-400"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($media['title']); ?></div>
                                <div class="text-sm text-gray-500 truncate max-w-xs"><?php echo htmlspecialchars($media['description']); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 capitalize">
                                    <?php echo htmlspecialchars($media['category']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo date('M j, Y', strtotime($media['date_added'])); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if (!empty($media['featured'])): ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Featured
                                    </span>
                                <?php else: ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Regular
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center justify-end space-x-4">
                                    <a href="edit_media.php?id=<?php echo $media['id']; ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                        <i class="fas fa-edit mr-1.5"></i> Edit
                                    </a>
                                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>?tab=media" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this media?');">
                                        <input type="hidden" name="action" value="delete_media">
                                        <input type="hidden" name="media_id" value="<?php echo $media['id']; ?>">
                                        <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                                            <i class="fas fa-trash mr-1.5"></i> Delete
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>