<?php
/**
 * Language support for multilingual functionality
 */

require_once 'config.php';

class Language {
    private static $translations = [];
    private static $currentLanguage = DEFAULT_LANGUAGE;
    private static $countryToLanguageMap = [
        // Spanish-speaking countries
        'ES' => 'es', 'MX' => 'es', 'AR' => 'es', 'CO' => 'es', 'PE' => 'es', 
        'VE' => 'es', 'CL' => 'es', 'EC' => 'es', 'GT' => 'es', 'CU' => 'es',
        'BO' => 'es', 'DO' => 'es', 'HN' => 'es', 'PY' => 'es', 'SV' => 'es',
        'NI' => 'es', 'CR' => 'es', 'PR' => 'es', 'PA' => 'es', 'UY' => 'es',
        
        // French-speaking countries
        'FR' => 'fr', 'BE' => 'fr', 'CH' => 'fr', 'LU' => 'fr',
        'MC' => 'fr', 'SN' => 'fr', 'CI' => 'fr', 'ML' => 'fr', 'CM' => 'fr',
        'MA' => 'fr', 'TN' => 'fr', 'DZ' => 'fr', 'MG' => 'fr', 'HT' => 'fr',
        
        // Portuguese-speaking countries
        'PT' => 'pt', 'AO' => 'pt', 'MZ' => 'pt', 'GW' => 'pt',
        'CV' => 'pt', 'ST' => 'pt',
        
        // Brazilian Portuguese
        'BR' => 'pt-br',
        
        // German-speaking countries
        'DE' => 'de', 'AT' => 'de', 'CH' => 'de', 'LI' => 'de',
        
        // Chinese-speaking regions
        'CN' => 'zh', 'TW' => 'zh', 'HK' => 'zh',
        
        // Russian-speaking countries
        'RU' => 'ru', 'BY' => 'ru', 'KZ' => 'ru', 'KG' => 'ru',
        
        // Arabic-speaking countries
        'SA' => 'ar', 'AE' => 'ar', 'QA' => 'ar', 'BH' => 'ar', 'KW' => 'ar',
        'OM' => 'ar', 'JO' => 'ar', 'LB' => 'ar', 'SY' => 'ar', 'IQ' => 'ar',
        'PS' => 'ar', 'YE' => 'ar', 'EG' => 'ar', 'SD' => 'ar', 'LY' => 'ar',
        'TN' => 'ar', 'MA' => 'ar', 'MR' => 'ar', 'SO' => 'ar', 'DJ' => 'ar',
        
        // Hindi-speaking countries
        'IN' => 'hi',
        
        // Urdu-speaking countries
        'PK' => 'ur',
        
        // Japanese-speaking countries
        'JP' => 'ja',
        
        // Korean-speaking countries
        'KR' => 'ko',
        
        // Indonesian-speaking countries
        'ID' => 'id',
        
        // Turkish-speaking countries
        'TR' => 'tr', 'CY' => 'tr',
        
        // Italian-speaking countries
        'IT' => 'it', 'SM' => 'it', 'VA' => 'it',
        
        // Polish-speaking countries
        'PL' => 'pl',
        
        // Swahili-speaking countries
        'KE' => 'sw', 'TZ' => 'sw', 'UG' => 'sw', 'CD' => 'sw', 'RW' => 'sw', 'BI' => 'sw',
        
        // Tagalog/Filipino-speaking countries
        'PH' => 'tl',
        
        // Hebrew-speaking countries
        'IL' => 'he',
        
        // English-speaking countries (default)
        'US' => 'en', 'GB' => 'en', 'CA' => 'en', 'AU' => 'en', 'NZ' => 'en',
        'IE' => 'en', 'ZA' => 'en', 'NG' => 'en', 'SG' => 'en', 'MY' => 'en'
    ];
    
    public static function init() {
        // Set language based on user preference or browser
        if (isset($_GET['lang']) && self::isValidLanguage($_GET['lang'])) {
            self::$currentLanguage = $_GET['lang'];
            $_SESSION['lang'] = $_GET['lang'];
            self::setLanguageCookie($_GET['lang']);
        } elseif (isset($_SESSION['lang']) && self::isValidLanguage($_SESSION['lang'])) {
            self::$currentLanguage = $_SESSION['lang'];
        } elseif (isset($_COOKIE['lang']) && self::isValidLanguage($_COOKIE['lang'])) {
            self::$currentLanguage = $_COOKIE['lang'];
            $_SESSION['lang'] = $_COOKIE['lang'];
        } else {
            // Try to detect from geolocation first
            $countryCode = self::getCountryCode();
            $geoLanguage = self::getLanguageFromCountry($countryCode);
            
            if ($geoLanguage) {
                self::$currentLanguage = $geoLanguage;
            } else {
                // Fall back to browser language detection
                $browserLang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '', 0, 2);
                if (self::isValidLanguage($browserLang)) {
                    self::$currentLanguage = $browserLang;
                }
            }
        }
        
        // Store the detected language in session and cookie
        $_SESSION['lang'] = self::$currentLanguage;
        self::setLanguageCookie(self::$currentLanguage);
        
        // Load language file
        self::loadLanguageFile(self::$currentLanguage);
    }
    
    /**
     * Get the user's country code using various methods
     * 
     * @return string|null Country code or null if not detected
     */
    private static function getCountryCode() {
        // Method 1: CloudFlare headers
        if (isset($_SERVER['HTTP_CF_IPCOUNTRY']) && !empty($_SERVER['HTTP_CF_IPCOUNTRY'])) {
            return $_SERVER['HTTP_CF_IPCOUNTRY'];
        }
        
        // Method 2: Try to get from IP using ipinfo.io service
        if (isset($_SERVER['REMOTE_ADDR']) && !empty($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
            
            // Skip for local IPs
            if (in_array($ip, ['127.0.0.1', '::1']) || strpos($ip, '192.168.') === 0) {
                return null;
            }
            
            try {
                // Using ipinfo.io free tier (1000 requests per day limit)
                $details = @file_get_contents("https://ipinfo.io/{$ip}/json");
                if ($details) {
                    $details = json_decode($details, true);
                    if (isset($details['country']) && !empty($details['country'])) {
                        return $details['country'];
                    }
                }
            } catch (Exception $e) {
                // Failed to get country, just continue
            }
        }
        
        return null;
    }
    
    /**
     * Get language code from country code using the mapping
     * 
     * @param string|null $countryCode The two-letter country code
     * @return string|null Language code or null if not mapped
     */
    private static function getLanguageFromCountry($countryCode) {
        if (!$countryCode) {
            return null;
        }
        
        $countryCode = strtoupper($countryCode);
        
        if (isset(self::$countryToLanguageMap[$countryCode])) {
            $langCode = self::$countryToLanguageMap[$countryCode];
            
            // Ensure the language is supported
            if (self::isValidLanguage($langCode)) {
                return $langCode;
            }
        }
        
        return null;
    }
    
    private static function isValidLanguage($lang) {
        $availableLanguages = unserialize(AVAILABLE_LANGUAGES);
        return isset($availableLanguages[$lang]);
    }
    
    private static function loadLanguageFile($lang) {
        $langFile = __DIR__ . "/lang/{$lang}.php";
        if (file_exists($langFile)) {
            self::$translations = include $langFile;
        } else {
            // Fallback to default language
            $defaultLangFile = __DIR__ . "/lang/" . DEFAULT_LANGUAGE . ".php";
            if (file_exists($defaultLangFile)) {
                self::$translations = include $defaultLangFile;
            }
        }
    }
    
    public static function get($key, $placeholders = []) {
        if (isset(self::$translations[$key])) {
            $text = self::$translations[$key];
            
            // Replace placeholders if any
            foreach ($placeholders as $placeholder => $value) {
                $text = str_replace('{' . $placeholder . '}', $value, $text);
            }
            
            return $text;
        }
        
        return $key; // Return the key if translation not found
    }
    
    public static function getCurrentLanguage() {
        return self::$currentLanguage;
    }
    
    public static function getAvailableLanguages() {
        return unserialize(AVAILABLE_LANGUAGES);
    }
    
    /**
     * Get the country to language mapping
     * 
     * @return array The mapping of country codes to language codes
     */
    public static function getCountryLanguageMap() {
        return self::$countryToLanguageMap;
    }
    
    /**
     * Set language cookie
     * 
     * @param string $lang Language code
     */
    private static function setLanguageCookie($lang) {
        $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
        
        // Check PHP version for appropriate cookie syntax
        if (PHP_VERSION_ID < 70300) {
            // Old way (PHP < 7.3)
            setcookie('lang', $lang, time() + 60 * 60 * 24 * 30, '/', '', $secure, true);
        } else {
            // New way (PHP >= 7.3)
            setcookie('lang', $lang, [
                'expires' => time() + 60 * 60 * 24 * 30,
                'path' => '/',
                'domain' => '',
                'secure' => $secure,
                'httponly' => true,
                'samesite' => 'Lax'
            ]);
        }
    }
}

// Initialize language
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
Language::init();

// Helper function for easier translation in templates
function __($key, $placeholders = []) {
    return Language::get($key, $placeholders);
}
?>
