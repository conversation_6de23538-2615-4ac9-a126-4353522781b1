# Live stream directory - minimal rules for proper JavaScript functionality

# Disable URL rewriting for this directory
RewriteEngine Off

# Set proper content types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType application/json .json
    AddType text/css .css
</IfModule>

# Allow cross-origin requests (for payment processing)
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# Ensure PHP files can be accessed directly
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

# Prevent caching of API responses
<FilesMatch "\.(php)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</FilesMatch>