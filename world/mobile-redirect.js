// Mobile Detection and Redirect Script
(function() {
    // Function to detect if device is mobile
    function isMobileDevice() {
        // Check user agent for mobile indicators
        const userAgent = navigator.userAgent.toLowerCase();
        const mobileKeywords = [
            'android', 'webos', 'iphone', 'ipad', 'ipod', 
            'blackberry', 'windows phone', 'mobile', 'tablet'
        ];
        
        // Check if any mobile keywords are found
        const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));
        
        // Check screen width (mobile-first approach)
        const isSmallScreen = window.innerWidth <= 768;
        
        // Check touch capability
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        // Return true if any mobile indicator is found
        return isMobileUA || (isSmallScreen && isTouchDevice);
    }
    
    // Function to get current page name
    function getCurrentPageName() {
        const path = window.location.pathname;
        const page = path.split('/').pop() || 'index.php';
        return page;
    }
    
    // Function to check if already on mobile version
    function isAlreadyOnMobile() {
        const currentPage = getCurrentPageName();
        return currentPage.includes('-mobile');
    }
    
    // Function to redirect to mobile version
    function redirectToMobile() {
        if (isAlreadyOnMobile()) {
            return; // Already on mobile version
        }
        
        const currentPage = getCurrentPageName();
        let mobileVersion = '';
        
        // Map desktop pages to mobile versions
        switch (currentPage) {
            case 'index.php':
            case '':
                mobileVersion = 'index-mobile.php';
                break;
            case 'host-crusade.php':
                mobileVersion = 'host-crusade-mobile.php';
                break;
            default:
                // If no mobile version exists, don't redirect
                return;
        }
        
        // Perform redirect
        if (mobileVersion) {
            window.location.href = mobileVersion;
        }
    }
    
    // Function to redirect to desktop version (for mobile pages)
    function redirectToDesktop() {
        if (!isAlreadyOnMobile()) {
            return; // Already on desktop version
        }
        
        const currentPage = getCurrentPageName();
        let desktopVersion = '';
        
        // Map mobile pages to desktop versions
        switch (currentPage) {
            case 'index-mobile.php':
                desktopVersion = 'index.php';
                break;
            case 'host-crusade-mobile.php':
                desktopVersion = 'host-crusade.php';
                break;
            default:
                return;
        }
        
        // Perform redirect
        if (desktopVersion) {
            window.location.href = desktopVersion;
        }
    }
    
    // Main redirect logic
    function handleRedirect() {
        // Check if user has manually chosen a version (via URL parameter)
        const urlParams = new URLSearchParams(window.location.search);
        const forceDesktop = urlParams.get('desktop') === 'true';
        const forceMobile = urlParams.get('mobile') === 'true';
        
        if (forceDesktop) {
            // User explicitly wants desktop version
            redirectToDesktop();
            return;
        }
        
        if (forceMobile) {
            // User explicitly wants mobile version
            redirectToMobile();
            return;
        }
        
        // Auto-detect and redirect
        if (isMobileDevice()) {
            redirectToMobile();
        } else {
            redirectToDesktop();
        }
    }
    
    // Function to add version switcher (stub function)
    function addVersionSwitcher() {
        // Stub function - version switcher functionality
        console.log('addVersionSwitcher called');
    }
    
    // Initialize when DOM is ready
    function init() {
        // Small delay to ensure page is fully loaded
        setTimeout(function() {
            handleRedirect();
            
            // Add version switcher after redirect logic
            setTimeout(addVersionSwitcher, 1000);
        }, 100);
    }
    
    // Run initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Handle window resize (in case user rotates device)
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            // Only redirect if screen size changes significantly
            const newIsMobile = isMobileDevice();
            const currentlyOnMobile = isAlreadyOnMobile();
            
            if (newIsMobile && !currentlyOnMobile) {
                redirectToMobile();
            } else if (!newIsMobile && currentlyOnMobile) {
                redirectToDesktop();
            }
        }, 500);
    });
})(); 